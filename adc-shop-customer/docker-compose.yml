version: '3.8'

services:
  # Customer Backend (Golang API)
  customer-backend:
    build:
      context: ./customer-backend
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - CUSTOMER_PORT=8081
      - GIN_MODE=release
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=restaurant_db
      - DB_SSL_MODE=disable
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      - CUSTOMER_DEFAULT_PAGE_SIZE=20
      - CUSTOMER_MAX_PAGE_SIZE=100
      - CUSTOMER_ENABLE_CENTER_PAGING=true
    depends_on:
      - postgres
    networks:
      - customer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "./customer-api", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Customer Frontend (Next.js)
  customer-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_CUSTOMER_API_URL=http://customer-backend:8081
      - NODE_ENV=production
    depends_on:
      - customer-backend
    networks:
      - customer-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=restaurant_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./customer-backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - customer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for caching, optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - customer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx (reverse proxy, optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - customer-frontend
      - customer-backend
    networks:
      - customer-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  customer-network:
    driver: bridge
